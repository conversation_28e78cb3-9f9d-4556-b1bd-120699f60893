"""
服务启动脚本
"""
import os
import sys
import asyncio
import platform
import argparse
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from mineru_api.main import app
from mineru_api.config import HOST, PORT
from mineru_api.services.sglang_manager import sglang_manager
from loguru import logger


async def check_and_start_sglang(auto_start: bool = False):
    """检查并启动 sglang 服务 (仅 Linux)"""
    if platform.system().lower() != "linux":
        logger.info("Windows 平台，跳过 sglang 服务检查")
        return

    logger.info("检查 sglang 服务状态...")

    if await sglang_manager.is_running():
        logger.success("✅ sglang 服务已运行")
        return

    if auto_start:
        logger.info("自动启动 sglang 服务...")
        success = await sglang_manager.start_server()
        if success:
            logger.success("✅ sglang 服务启动成功")
        else:
            logger.warning("⚠️ sglang 服务启动失败，将使用 pipeline 模式")
    else:
        logger.info("sglang 服务未运行")
        logger.info("提示: 使用 --auto-sglang 参数可自动启动 sglang 服务")
        logger.info("或手动启动: ./scripts/start_sglang.sh")


def check_database_initialization():
    """检查数据库初始化状态"""
    try:
        logger.info("检查数据库初始化状态...")

        # 检查必要的目录
        from mineru_api.config import BASE_DIR
        data_dir = BASE_DIR / "data"
        if not data_dir.exists():
            logger.info("创建数据目录...")
            data_dir.mkdir(parents=True, exist_ok=True)

        # 检查任务历史数据库
        from mineru_api.services.history_service import TaskHistoryService
        history_service = TaskHistoryService()
        logger.info(f"✅ 任务历史数据库: {history_service.db_path}")

        # 检查认证数据库
        from mineru_api.config import ENABLE_AUTH
        if ENABLE_AUTH:
            from mineru_api.auth.manager import init_auth_manager, get_auth_config
            auth_config = get_auth_config()
            init_auth_manager(auth_config)
            logger.info("✅ 认证数据库初始化完成")

        logger.success("✅ 数据库检查通过")
        return True

    except Exception as e:
        logger.error(f"❌ 数据库初始化检查失败: {e}")
        logger.error("请运行: python scripts/init_database.py")
        return False


def main():
    """启动服务"""
    parser = argparse.ArgumentParser(description="MineruAPI 服务启动器")
    parser.add_argument("--host", type=str, default=HOST, help="服务监听地址")
    parser.add_argument("--port", type=int, default=PORT, help="服务端口")
    parser.add_argument("--auto-sglang", action="store_true",
                        help="自动启动 sglang 服务 (仅 Linux)")
    parser.add_argument("--no-sglang-check", action="store_true",
                        help="跳过 sglang 服务检查")

    args = parser.parse_args()

    logger.info("=" * 60)
    logger.info("MineruAPI 服务启动中...")
    logger.info(f"平台: {platform.system()}")
    logger.info(f"服务地址: http://{args.host}:{args.port}")
    logger.info("=" * 60)

    # 检查数据库初始化
    if not check_database_initialization():
        logger.error("数据库初始化失败，服务无法启动")
        sys.exit(1)

    # 检查 sglang 服务
    if not args.no_sglang_check:
        try:
            asyncio.run(check_and_start_sglang(args.auto_sglang))
        except Exception as e:
            logger.error(f"sglang 服务检查失败: {e}")

    # 显示支持的后端
    logger.info("\n📋 支持的后端模式:")
    logger.info("  - pipeline: 传统模式 (CPU/GPU)")
    if platform.system().lower() == "linux":
        logger.info("  - vlm/sglang: 加速模式 (需要 sglang 服务)")
        logger.info("  - vlm-transformers: VLM 模式")
    logger.info("\n🌐 管理接口:")
    logger.info(f"  - 健康检查: http://{args.host}:{args.port}/health")
    logger.info(f"  - API 文档: http://{args.host}:{args.port}/docs")
    if platform.system().lower() == "linux":
        logger.info(f"  - sglang 状态: http://{args.host}:{args.port}/sglang/status")

    # 启动服务
    try:
        logger.info("\n🚀 启动服务...")
        app.run(host=args.host, port=args.port)
    except KeyboardInterrupt:
        logger.info("\n👋 服务已停止")

        # 清理 sglang 服务
        if platform.system().lower() == "linux" and args.auto_sglang:
            logger.info("清理 sglang 服务...")
            try:
                asyncio.run(sglang_manager.stop_server())
            except Exception as e:
                logger.error(f"停止 sglang 服务失败: {e}")

    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
