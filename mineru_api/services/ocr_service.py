"""
OCR 解析服务 - 封装 demo 代码的解析逻辑
"""
import asyncio
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import Dict, Optional
from urllib.parse import quote

from loguru import logger

from .doc_converter import doc_converter, ConvertError
from .parser import do_parse
from .sglang_manager import sglang_manager
from ..config import OUTPUT_DIR, TEMP_DIR, BACKEND_MAPPING, SGLANG_URL, FILE_ACCESS_PREFIX
from ..models import OCRRequest, TaskResult
from ..utils.async_utils import StructuredLogger, timing_decorator


class OCRService:
    """OCR 解析服务"""

    def __init__(self):
        self.output_base_dir = OUTPUT_DIR
        self.temp_dir = TEMP_DIR
        self._executor = ThreadPoolExecutor()  # ✅ 初始化线程池

    @timing_decorator("ocr_processing")
    async def process_ocr(self, task_id: str, request: OCRRequest) -> Optional[TaskResult, None]:
        """处理 OCR 解析请求"""
        # 详细的开始日志
        StructuredLogger.log_task_event(
            task_id, "ocr_processing_started",
            file_name=request.file_name,
            file_size=len(request.file_content),
            backend=request.backend,
            method=request.method,
            lang=request.lang,
            formula_enable=request.formula_enable,
            table_enable=request.table_enable
        )

        logger.info(f"开始处理任务 {task_id}: {request.file_name} ({len(request.file_content)} bytes)")

        # 创建任务专用目录
        task_output_dir = self.output_base_dir / task_id
        task_output_dir.mkdir(exist_ok=True)

        # 解码文件内容并保存到临时文件
        temp_file_path = self._save_temp_file(request.file_content, request.file_name)

        # 检查文件格式并进行转换
        pdf_file_path, original_file_name = self._prepare_pdf_file(temp_file_path, task_id, request.file_name)

        # 处理后端配置
        backend, server_url = await self._prepare_backend(task_id, request.backend)

        try:
            # 准备解析参数
            pdf_file_names = [Path(original_file_name).stem]

            # 读取PDF文件内容
            with open(pdf_file_path, 'rb') as f:
                pdf_bytes = f.read()

            pdf_bytes_list = [pdf_bytes]
            p_lang_list = [request.lang]

            # 调用 demo 的解析函数
            loop = asyncio.get_running_loop()
            await loop.run_in_executor(
                self._executor,
                lambda: do_parse(
                    output_dir=str(task_output_dir),
                    pdf_file_names=pdf_file_names,
                    pdf_bytes_list=pdf_bytes_list,
                    p_lang_list=p_lang_list,
                    backend=backend,
                    parse_method=request.method,
                    server_url=server_url,
                    p_formula_enable=request.formula_enable,
                    p_table_enable=request.table_enable,
                    f_dump_md=request.dump_md,
                    f_dump_content_list=request.dump_content_list,
                    f_dump_middle_json=request.dump_middle_json,
                    start_page_id=request.start_page_id,
                    end_page_id=request.end_page_id
                )
            )

            # 收集生成的文件
            generated_files = self._collect_generated_files(task_id, task_output_dir)

            # 创建结果对象
            result = TaskResult(
                task_id=task_id,
                file_name=request.file_name,
                output_dir=str(task_output_dir),
                files=generated_files,
                metadata={
                    "original_file_name": request.file_name,
                    "converted_from": self._get_file_type(request.file_name),
                    "lang": request.lang,
                    "backend": backend,
                    "original_backend": request.backend,
                    "server_url": server_url,
                    "method": request.method,
                    "formula_enable": request.formula_enable,
                    "table_enable": request.table_enable,
                    "start_page_id": request.start_page_id,
                    "end_page_id": request.end_page_id
                }
            )

            # 成功完成日志
            StructuredLogger.log_task_event(
                task_id, "ocr_processing_completed",
                generated_files=list(generated_files.keys()),
                output_dir=str(task_output_dir),
                files_count=len(generated_files)
            )

            logger.info(f"任务 {task_id} 处理完成，生成文件: {list(generated_files.keys())}")
            return result

        except Exception as e:
            # 详细的错误记录
            StructuredLogger.log_error(
                task_id, e,
                context={
                    "file_name": request.file_name,
                    "file_size": len(request.file_content),
                    "backend": request.backend,
                    "method": request.method,
                    "temp_file": str(temp_file_path),
                    "output_dir": str(task_output_dir)
                }
            )
            logger.error(f"任务 {task_id} 处理失败: {e}")
            raise
        finally:
            # 清理临时文件
            if temp_file_path.exists():
                temp_file_path.unlink()
                StructuredLogger.log_task_event(
                    task_id, "temp_file_cleaned",
                    temp_file=str(temp_file_path)
                )

            # 清理转换后的PDF文件（如果不是原始PDF）
            if pdf_file_path != temp_file_path and pdf_file_path.exists():
                pdf_file_path.unlink()
                StructuredLogger.log_task_event(
                    task_id, "converted_pdf_cleaned",
                    converted_file=str(pdf_file_path)
                )

    def _save_temp_file(self, file_content: bytes, file_name: str) -> Path:
        """保存临时文件"""
        temp_file_path = self.temp_dir / f"temp_{file_name}"
        with open(temp_file_path, 'wb') as f:
            f.write(file_content)
        return temp_file_path

    def _get_file_type(self, file_name: str) -> str:
        """获取文件类型"""
        suffix = Path(file_name).suffix.lower()
        if suffix in doc_converter.OFFICE_FORMATS:
            return "office"
        elif suffix in doc_converter.IMAGE_FORMATS:
            return "image"
        elif suffix in doc_converter.PDF_FORMATS:
            return "pdf"
        else:
            return "unknown"

    def _prepare_pdf_file(self, temp_file_path: Path, task_id: str, original_file_name: str) -> tuple[Path, str]:
        """准备PDF文件 - 如果不是PDF则转换"""
        file_type = self._get_file_type(original_file_name)

        StructuredLogger.log_task_event(
            task_id, "file_type_detected",
            file_name=original_file_name,
            file_type=file_type,
            needs_conversion=file_type != "pdf"
        )

        if file_type == "pdf":
            # 已经是PDF，直接使用
            logger.info(f"文件已是PDF格式: {original_file_name}")
            return temp_file_path, original_file_name

        elif file_type in ["office", "image"]:
            # 需要转换
            logger.info(f"开始转换文件: {original_file_name} ({file_type} -> PDF)")

            try:
                # 创建转换输出目录
                convert_output_dir = self.temp_dir / f"convert_{task_id}"
                convert_output_dir.mkdir(exist_ok=True)

                # 执行转换
                pdf_file_path = doc_converter.convert_to_pdf(
                    temp_file_path,
                    convert_output_dir,
                    task_id
                )

                StructuredLogger.log_task_event(
                    task_id, "file_conversion_completed",
                    original_file=original_file_name,
                    converted_file=pdf_file_path.name,
                    conversion_type=f"{file_type}_to_pdf"
                )

                logger.info(f"文件转换完成: {original_file_name} -> {pdf_file_path.name}")
                return pdf_file_path, original_file_name

            except ConvertError as e:
                StructuredLogger.log_error(
                    task_id, e,
                    context={
                        "original_file": original_file_name,
                        "file_type": file_type,
                        "conversion_stage": "document_conversion"
                    }
                )
                raise Exception(f"文档转换失败: {str(e)}")

        else:
            # 不支持的格式
            error_msg = f"不支持的文件格式: {original_file_name}"
            StructuredLogger.log_task_event(
                task_id, "unsupported_file_format",
                file_name=original_file_name,
                file_type=file_type
            )
            raise Exception(error_msg)

    async def _prepare_backend(self, task_id: str, requested_backend: str) -> tuple[str, str]:
        """准备后端配置"""
        # 映射后端名称
        backend = BACKEND_MAPPING.get(requested_backend, requested_backend)
        server_url = None

        StructuredLogger.log_task_event(
            task_id, "backend_mapping",
            requested_backend=requested_backend,
            mapped_backend=backend
        )

        # 如果是 sglang 相关后端，需要确保服务运行
        if backend == "vlm-sglang-client":
            server_url = SGLANG_URL

            StructuredLogger.log_task_event(
                task_id, "sglang_backend_requested",
                server_url=server_url
            )

            # 检查 sglang 服务是否运行
            if not await sglang_manager.is_running():
                logger.warning("sglang 服务未运行，尝试启动...")

                success = await sglang_manager.start_server()
                if not success:
                    # 启动失败，回退到 pipeline
                    logger.warning("sglang 服务启动失败，回退到 pipeline 模式")
                    backend = "pipeline"
                    server_url = None

                    StructuredLogger.log_task_event(
                        task_id, "backend_fallback",
                        original_backend=requested_backend,
                        fallback_backend=backend,
                        reason="sglang_startup_failed"
                    )
                else:
                    StructuredLogger.log_task_event(
                        task_id, "sglang_server_ready",
                        server_url=server_url
                    )
            else:
                StructuredLogger.log_task_event(
                    task_id, "sglang_server_already_running",
                    server_url=server_url
                )

        logger.info(f"使用后端: {backend}" + (f" (server: {server_url})" if server_url else ""))

        return backend, server_url

    def _collect_generated_files(self, task_id: str, output_dir: Path) -> Dict[str, str]:
        """收集生成的文件并生成可访问 URL（自动编码路径）"""
        files = {}

        for subdir in output_dir.iterdir():
            if subdir.is_dir():
                for file_path in subdir.rglob("*"):
                    if file_path.is_file():
                        file_type = self._determine_file_type(file_path)

                        # 相对于 output_dir（即 task_id 子目录）
                        relative_path = file_path.relative_to(output_dir)

                        # URL 拼接（路径编码）
                        encoded_path = quote(relative_path.as_posix())
                        public_url = f"{FILE_ACCESS_PREFIX}/{task_id}/{encoded_path}"

                        files[file_type] = public_url

        return files

    def _determine_file_type(self, file_path: Path) -> str:
        """确定文件类型"""
        suffix = file_path.suffix.lower()
        name = file_path.stem.lower()

        if suffix == '.md':
            return 'markdown'
        elif suffix == '.json':
            if 'content_list' in name:
                return 'content_list'
            elif 'middle' in name:
                return 'middle_json'
            elif 'model' in name:
                return 'model_output'
            else:
                return 'json'
        elif suffix == '.pdf':
            if 'layout' in name:
                return 'layout_bbox'
            elif 'span' in name:
                return 'span_bbox'
            elif 'origin' in name:
                return 'original_pdf'
            else:
                return 'pdf'
        elif suffix in ['.png', '.jpg', '.jpeg']:
            return 'image'
        else:
            return 'other'


# 全局 OCR 服务实例
ocr_service = OCRService()
