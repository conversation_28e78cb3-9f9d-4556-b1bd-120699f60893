"""
回调状态跟踪器 - 确保回调可靠性的新模块
不修改现有代码，通过独立模块提供回调保障
"""
import asyncio
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
from loguru import logger

from ..config import CALLBACK_RETRY_TIMES, CALLBACK_RETRY_DELAY, BASE_DIR
from ..models import TaskStatus


@dataclass
class CallbackRecord:
    """回调记录"""
    task_id: str
    callback_url: str
    headers: Optional[Dict[str, str]]
    status: str  # pending, success, failed, abandoned
    attempts: int
    last_attempt_at: Optional[datetime]
    last_error: Optional[str]
    created_at: datetime
    max_retries: int = CALLBACK_RETRY_TIMES


class CallbackTracker:
    """回调状态跟踪器 - 独立的回调保障系统"""
    
    def __init__(self):
        self._records: Dict[str, CallbackRecord] = {}
        self._lock = threading.Lock()
        self._storage_file = BASE_DIR / "data" / "callback_records.json"
        self._storage_file.parent.mkdir(exist_ok=True)
        self._running = False
        self._retry_thread = None
        
        # 启动时加载历史记录
        self._load_records()
        
    def start_retry_worker(self):
        """启动重试工作线程"""
        if self._running:
            return
            
        self._running = True
        self._retry_thread = threading.Thread(
            target=self._retry_worker, 
            daemon=True, 
            name="callback-retry-worker"
        )
        self._retry_thread.start()
        logger.info("回调重试工作线程已启动")
    
    def stop_retry_worker(self):
        """停止重试工作线程"""
        self._running = False
        if self._retry_thread:
            self._retry_thread.join(timeout=5)
        logger.info("回调重试工作线程已停止")
    
    def track_callback(self, task_id: str, callback_url: str, 
                      headers: Optional[Dict[str, str]] = None):
        """开始跟踪一个回调"""
        with self._lock:
            record = CallbackRecord(
                task_id=task_id,
                callback_url=callback_url,
                headers=headers,
                status="pending",
                attempts=0,
                last_attempt_at=None,
                last_error=None,
                created_at=datetime.now()
            )
            self._records[task_id] = record
            logger.info(f"开始跟踪回调: {task_id}")
            self._save_records()
    
    def mark_callback_success(self, task_id: str):
        """标记回调成功"""
        with self._lock:
            if task_id in self._records:
                self._records[task_id].status = "success"
                self._records[task_id].last_attempt_at = datetime.now()
                logger.info(f"回调成功: {task_id}")
                self._save_records()
    
    def mark_callback_failed(self, task_id: str, error: str):
        """标记回调失败"""
        with self._lock:
            if task_id in self._records:
                record = self._records[task_id]
                record.attempts += 1
                record.last_attempt_at = datetime.now()
                record.last_error = error
                
                if record.attempts >= record.max_retries:
                    record.status = "abandoned"
                    logger.error(f"回调彻底失败，已放弃: {task_id}, 错误: {error}")
                else:
                    record.status = "failed"
                    logger.warning(f"回调失败，将重试: {task_id}, 尝试次数: {record.attempts}")
                
                self._save_records()
    
    def get_pending_callbacks(self) -> List[CallbackRecord]:
        """获取待重试的回调"""
        with self._lock:
            pending = []
            now = datetime.now()
            
            for record in self._records.values():
                if record.status == "failed":
                    # 检查是否到了重试时间
                    if (record.last_attempt_at is None or 
                        now - record.last_attempt_at >= timedelta(seconds=CALLBACK_RETRY_DELAY)):
                        pending.append(record)
            
            return pending
    
    def get_callback_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取回调状态"""
        with self._lock:
            if task_id in self._records:
                record = self._records[task_id]
                return {
                    "task_id": record.task_id,
                    "status": record.status,
                    "attempts": record.attempts,
                    "last_attempt_at": record.last_attempt_at.isoformat() if record.last_attempt_at else None,
                    "last_error": record.last_error,
                    "created_at": record.created_at.isoformat()
                }
            return None
    
    def cleanup_old_records(self, max_age_hours: int = 24):
        """清理旧记录"""
        with self._lock:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            to_remove = []
            
            for task_id, record in self._records.items():
                if (record.status in ["success", "abandoned"] and 
                    record.created_at < cutoff_time):
                    to_remove.append(task_id)
            
            for task_id in to_remove:
                del self._records[task_id]
            
            if to_remove:
                logger.info(f"清理了 {len(to_remove)} 条旧回调记录")
                self._save_records()
    
    def _retry_worker(self):
        """重试工作线程"""
        logger.info("回调重试工作线程开始运行")
        
        while self._running:
            try:
                pending_callbacks = self.get_pending_callbacks()
                
                for record in pending_callbacks:
                    if not self._running:
                        break
                    
                    try:
                        # 执行重试
                        self._retry_callback(record)
                    except Exception as e:
                        logger.error(f"重试回调时出错: {record.task_id}, 错误: {e}")
                
                # 定期清理旧记录
                if int(time.time()) % 3600 == 0:  # 每小时清理一次
                    self.cleanup_old_records()
                
                # 等待一段时间再检查
                time.sleep(10)
                
            except Exception as e:
                logger.error(f"回调重试工作线程出错: {e}")
                time.sleep(30)  # 出错后等待更长时间
    
    def _retry_callback(self, record: CallbackRecord):
        """重试单个回调"""
        from .task_manager import task_manager
        from ..utils.callback_utils import callback_handler
        
        logger.info(f"重试回调: {record.task_id}, 第 {record.attempts + 1} 次尝试")
        
        # 检查任务是否还存在
        task_status = task_manager.get_task_status(record.task_id)
        if not task_status:
            logger.warning(f"任务不存在，停止重试回调: {record.task_id}")
            self.mark_callback_failed(record.task_id, "任务不存在")
            return
        
        try:
            # 使用现有的回调处理器
            callback_handler.send_callback_sync(
                record.task_id,
                record.callback_url,
                record.headers
            )
            
            # 注意：这里无法直接知道回调是否成功
            # 需要通过其他方式验证，比如检查日志或者修改回调处理器
            
        except Exception as e:
            self.mark_callback_failed(record.task_id, str(e))
    
    def _save_records(self):
        """保存记录到文件"""
        try:
            data = {}
            for task_id, record in self._records.items():
                record_dict = asdict(record)
                # 转换datetime为字符串
                if record_dict['created_at']:
                    record_dict['created_at'] = record_dict['created_at'].isoformat()
                if record_dict['last_attempt_at']:
                    record_dict['last_attempt_at'] = record_dict['last_attempt_at'].isoformat()
                data[task_id] = record_dict
            
            with open(self._storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存回调记录失败: {e}")
    
    def _load_records(self):
        """从文件加载记录"""
        try:
            if not self._storage_file.exists():
                return
            
            with open(self._storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for task_id, record_dict in data.items():
                # 转换字符串为datetime
                if record_dict['created_at']:
                    record_dict['created_at'] = datetime.fromisoformat(record_dict['created_at'])
                if record_dict['last_attempt_at']:
                    record_dict['last_attempt_at'] = datetime.fromisoformat(record_dict['last_attempt_at'])
                
                record = CallbackRecord(**record_dict)
                self._records[task_id] = record
            
            logger.info(f"加载了 {len(self._records)} 条回调记录")
            
        except Exception as e:
            logger.error(f"加载回调记录失败: {e}")


# 全局回调跟踪器实例
callback_tracker = CallbackTracker()
