"""
MineruAPI - 基于 LitServe 的 OCR 解析服务
"""
import sys
from pathlib import Path

# 将项目根目录添加到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import litserve as ls
from loguru import logger
import atexit

from mineru_api.api import router
from mineru_api.api.auth_routes import auth_router
from mineru_api.api.task_status import router as task_status_router
from mineru_api.config import HOST, PORT, LOG_LEVEL, LOG_FILE, ENABLE_AUTH, get_auth_config
from mineru_api.services.mineru_server import api
from mineru_api.utils.callback_utils import callback_handler
from mineru_api.services.callback_service import callback_service
from mineru_api.utils.callback_enhancer import init_callback_enhancements, cleanup_callback_enhancements

# 配置日志
logger.add(LOG_FILE, rotation="10 MB", retention="7 days", level=LOG_LEVEL)

# 初始化认证管理器
if ENABLE_AUTH:
    from mineru_api.auth.manager import init_auth_manager
    auth_config = get_auth_config()
    init_auth_manager(auth_config)
    logger.info("认证功能已启用")
else:
    logger.warning("认证功能已禁用，所有接口无需认证")

# 创建 LitServer 实例
app = ls.LitServer(api, accelerator="cpu")

# 配置 LitServer 的日志级别以减少冗余输出
import logging
litserver_logger = logging.getLogger("litserve")
litserver_logger.setLevel(logging.WARNING)

# 添加全局认证中间件（保护所有接口包括/predict）
if ENABLE_AUTH:
    from mineru_api.auth.global_middleware import create_global_auth_middleware
    from mineru_api.auth.startup import setup_auth_startup_handler

    app.app = create_global_auth_middleware(app.app)
    app.app = setup_auth_startup_handler(app.app)

# 注册路由
app.app.include_router(router)
app.app.include_router(auth_router)
app.app.include_router(task_status_router)

# 初始化回调增强功能
init_callback_enhancements()


def cleanup_resources():
    """清理资源"""
    logger.info("正在清理资源...")
    try:
        # 清理回调增强功能
        cleanup_callback_enhancements()

        # 关闭回调处理器
        callback_handler.shutdown()

        # 关闭回调服务客户端
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(callback_service.close())
            loop.close()
        except Exception as e:
            logger.warning(f"关闭回调服务时出错: {e}")

        logger.info("资源清理完成")
    except Exception as e:
        logger.error(f"资源清理失败: {e}")


# 注册退出处理器
atexit.register(cleanup_resources)

if __name__ == "__main__":
    # 启动服务
    logger.info(f"启动 MineruAPI 服务: {HOST}:{PORT}")
    try:
        app.run(host=HOST, port=PORT, pretty_logs=True)
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    finally:
        cleanup_resources()
