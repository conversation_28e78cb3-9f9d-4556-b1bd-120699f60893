# -*- encoding: utf-8 -*-
"""
@File   :manage.py
@Time   :2025/6/6 16:03
<AUTHOR>
"""

import time
import asyncio
from typing import Optional, List, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect, Depends, Query, HTTPException, APIRouter, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, func, desc, asc
from loguru import logger
from pydantic import BaseModel
from datetime import datetime

# 正确的导入路径
from almond_parser.config import settings
from almond_parser.db import get_db
from almond_parser.db.models import User, Document, DocumentStatus, MinerUNode
from almond_parser.schemas import NodeStatus
from almond_parser.utils.auth import verify_password, create_access_token, get_current_user, get_password_hash
from almond_parser.services.mineru_node_service import MinerUNodeService
from almond_parser.tasks.document_tasks import process_document

from almond_parser.schemas.mineru_node import MinerUNodeStats

router = APIRouter(prefix="/manage", tags=["管理后台"])


class LoginRequest(BaseModel):
    username: str
    password: str


class Token(BaseModel):
    access_token: str
    token_type: str


class CreateAdminRequest(BaseModel):
    username: str
    password: str
    super_key: str  # 用于验证创建管理员的权限


class RegisterRequest(BaseModel):
    username: str
    password: str
    confirm_password: str
    is_admin: bool = False  # 是否创建管理员账号
    super_key: str = None  # 如果创建管理员账号，需要提供super_key


@router.post("/login", response_model=Token)
async def login(request: LoginRequest, db: AsyncSession = Depends(get_db)):
    """管理员登录接口"""
    # 查找用户
    stmt = select(User).where(User.username == request.username)
    result = await db.execute(stmt)
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=401, detail="用户名或密码错误")

    # 验证密码
    if not verify_password(request.password, user.password):
        raise HTTPException(status_code=401, detail="用户名或密码错误")

    # 验证是否是管理员
    if not user.is_admin:
        raise HTTPException(status_code=403, detail="非管理员用户无法登录")

    # 验证用户是否激活
    if not user.is_active:
        raise HTTPException(status_code=400, detail="用户已被禁用")

    # 创建访问令牌
    token_data = {
        "sub": user.username,
        "user_id": user.id,
        "is_admin": user.is_admin
    }
    access_token = create_access_token(token_data)

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": str(user.id),
            "username": user.username
        }
    }


@router.get("/me")
async def read_users_me(current_user: dict = Depends(get_current_user)):
    """获取当前登录用户信息"""
    return current_user


@router.get("/documents", summary="查询文档记录")
async def query_documents(
        document_id: Optional[str] = Query(None, description="文档ID"),
        batch_id: Optional[str] = Query(None, description="批次ID"),
        status: Optional[str] = Query(None, description="状态"),
        file_name: Optional[str] = Query(None, description="文件名"),
        sort_by: Optional[str] = Query(None, description="排序字段"),
        sort_order: Optional[str] = Query('desc', description="排序方向(asc/desc)"),
        page: int = Query(1, ge=1),
        page_size: int = Query(10, ge=1, le=100),
        current_user: dict = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
):
    """查询文档记录 - 需要管理员权限"""
    if not current_user.get("is_admin"):
        raise HTTPException(status_code=403, detail="需要管理员权限")

    filters = []
    if document_id:
        filters.append(Document.document_id == document_id)
    if batch_id:
        filters.append(Document.batch_id == batch_id)
    if status:
        filters.append(Document.status == status)
    if file_name:
        filters.append(Document.file_name.like(f"%{file_name}%"))

    # 处理排序
    if sort_by:
        sort_column = getattr(Document, sort_by, Document.created_at)
        order_func = desc if sort_order == 'desc' else asc
        order_by = order_func(sort_column)
    else:
        order_by = Document.created_at.desc()

    stmt = select(Document).where(and_(*filters)).order_by(order_by)
    total_stmt = select(func.count()).select_from(Document).where(and_(*filters))

    total_result = await db.execute(total_stmt)
    total = total_result.scalar()

    result = await db.execute(stmt.offset((page - 1) * page_size).limit(page_size))
    documents = result.scalars().all()

    # 转换为字典格式
    items = []
    for doc in documents:
        items.append({
            "id": doc.id,
            "document_id": doc.document_id,
            "batch_id": doc.batch_id,
            "filename": doc.file_name,
            "file_size": doc.file_size,
            "file_type": doc.file_type,
            "status": doc.status.value if hasattr(doc.status, 'value') else doc.status,
            "created_at": doc.created_at.isoformat() if doc.created_at else None,
            "updated_at": doc.updated_at.isoformat() if doc.updated_at else None,
            "user_id": doc.user_id,
            "error_message": doc.error_message,
            "retry_count": doc.retry_count,
            "max_retries": doc.max_retries
        })

    return {
        "total": total,
        "items": items,
        "page": page,
        "page_size": page_size
    }


@router.post("/retry/{document_id}", summary="手动重试解析任务")
async def manual_retry(
        document_id: str,
        current_user: dict = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
):
    """手动重试解析任务 - 需要管理员权限"""
    if not current_user.get("is_admin"):
        raise HTTPException(status_code=403, detail="需要管理员权限")

    # 查询文档是否存在
    logger.info(f'{document_id} 重试')
    result = await db.execute(select(Document).where(Document.document_id == document_id))
    document = result.scalar_one_or_none()
    if not document:
        logger.error(f'{document_id} 不存在')
        raise HTTPException(status_code=404, detail=f"文档 ID {document_id} 不存在")

    # 使用增强的 ARQ 异步任务重试
    try:
        from almond_parser.tasks.arq_app import arq_manager

        # 提交增强的异步任务（支持兼容性匹配）
        job_id = await arq_manager.enqueue_task(
            'enhanced_process_document',
            document_id=document_id,
            user_id=str(current_user.get("user_id", "admin")),
            service_type="auto",
            parse_mode="auto",
            config={}
        )

        logger.info(f"✅ 文件 {document.file_name} 增强任务ID：{job_id}")

        return {
            "message": f"已成功调度文档 ID {document_id} 的增强解析任务",
            "task_id": job_id,
            "features": ["vlm_fallback", "node_retry", "enhanced_logging", "compatibility_matching"]
        }
    except Exception as e:
        logger.error(f"任务调度失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"任务调度失败: {str(e)}")


@router.websocket("/ws/logs/document/{document_id}")
async def stream_document_log(websocket: WebSocket, document_id: str):
    """实时推送文档解析日志"""
    await websocket.accept()

    # 使用配置中的日志文件路径
    from pathlib import Path
    log_file_path = Path(settings.LOG_DIR) / "almond_parser.log"

    logger.info(f"[doc:{document_id}] 客户端已连接，开始读取日志文件 {log_file_path}")

    try:
        start_push = False

        async def async_line_generator(path):
            try:
                with open(path, "r", encoding="utf-8") as f:
                    # 先读取历史内容
                    while True:
                        line = f.readline()
                        if not line:
                            break
                        yield line

                    # 跳到文件末尾后实时监听新增日志
                    while True:
                        line = f.readline()
                        if line:
                            yield line
                        else:
                            await asyncio.sleep(0.2)  # 非阻塞等待新日志写入
            except FileNotFoundError:
                logger.warning(f"日志文件不存在: {path}")
                yield f"日志文件不存在: {path}\n"

        async for line in async_line_generator(log_file_path):
            if f"[doc:{document_id}]" in line or f"document_id={document_id}" in line:
                start_push = True

            if start_push:
                await websocket.send_text(line)

            if ("任务结束" in line or "任务完成" in line) and (
                    f"[doc:{document_id}]" in line or f"document_id={document_id}" in line):
                logger.info(f"[doc:{document_id}] 🏁 推送完成（保持连接）")
                break

        # 保持连接（如果你希望任务结束后用户手动关闭）
        while True:
            await asyncio.sleep(1)

    except WebSocketDisconnect:
        logger.info(f"[doc:{document_id}] 客户端断开连接")
    except Exception as e:
        logger.error(f"[doc:{document_id}] 日志推送异常: {e}")
        await websocket.close()


@router.post("/register", summary="用户注册")
async def register(
        request: RegisterRequest,
        db: AsyncSession = Depends(get_db)
):
    """用户注册接口，支持创建普通用户和管理员用户"""
    # 验证两次密码是否一致
    if request.password != request.confirm_password:
        raise HTTPException(status_code=400, detail="两次输入的密码不一致")

    # 如果要创建管理员账号，验证super_key
    if request.is_admin:
        if not request.super_key:
            raise HTTPException(status_code=400, detail="创建管理员账号需要提供super_key")
        if request.super_key != settings.SUPER_KEY:
            raise HTTPException(status_code=403, detail="无权限创建管理员")

    try:
        # 检查用户名是否已存在
        stmt = select(User).where(User.username == request.username)
        result = await db.execute(stmt)
        if result.scalars().first():
            raise HTTPException(status_code=400, detail="用户名已存在")

        # 创建新用户
        user = User(
            username=request.username,
            password=get_password_hash(request.password),
            is_admin=request.is_admin,
            is_active=True
        )

        db.add(user)
        await db.commit()
        await db.refresh(user)

        # 创建访问令牌
        token_data = {
            "sub": user.username,
            "user_id": user.id,
            "is_admin": user.is_admin
        }
        access_token = create_access_token(token_data)

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": str(user.id),
                "username": user.username
            }
        }

    except Exception as e:
        await db.rollback()
        logger.error(f"注册失败: {str(e)}")
        raise HTTPException(status_code=500, detail="注册失败，请稍后重试")


@router.get("/mineru/nodes", summary="查看MinerU节点状态")
async def get_mineru_nodes(
        current_user: dict = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
):
    """查看所有MinerU节点的状态"""
    if not current_user.get("is_admin"):
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        # 获取所有节点信息
        result = await db.execute(select(MinerUNode))
        nodes = result.scalars().all()

        # 构建节点信息
        node_details = []
        for node in nodes:
            node_details.append({
                "id": node.id,
                "name": node.name,
                "host": node.host,
                "port": node.port,
                "base_url": node.base_url,
                "parse_mode": node.parse_mode.value if hasattr(node.parse_mode, 'value') else node.parse_mode,
                "status": node.status.value if hasattr(node.status, 'value') else node.status,
                "current_tasks": getattr(node, 'current_tasks', 0),
                "max_concurrent_tasks": node.max_concurrent_tasks,
                "total_tasks": getattr(node, 'total_tasks', 0),
                "success_tasks": getattr(node, 'success_tasks', 0),
                "failed_tasks": getattr(node, 'failed_tasks', 0),
                "priority": node.priority,
                "is_enabled": node.is_enabled,
                "created_at": node.created_at.isoformat() if node.created_at else None,
                "updated_at": node.updated_at.isoformat() if node.updated_at else None
            })

        return {
            "total_nodes": len(nodes),
            "nodes": node_details,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.exception(f"获取节点状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@router.get("/system/stats", summary="系统统计信息")
async def get_system_stats(
        current_user: dict = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
):
    """获取系统统计信息"""
    if not current_user.get("is_admin"):
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        # 文档统计
        doc_total = await db.execute(select(func.count()).select_from(Document))
        total_documents = doc_total.scalar()

        doc_pending = await db.execute(
            select(func.count()).select_from(Document).where(Document.status == DocumentStatus.PENDING)
        )
        pending_documents = doc_pending.scalar()

        doc_processing = await db.execute(
            select(func.count()).select_from(Document).where(Document.status == DocumentStatus.PROCESSING)
        )
        processing_documents = doc_processing.scalar()

        doc_completed = await db.execute(
            select(func.count()).select_from(Document).where(Document.status == DocumentStatus.COMPLETED)
        )
        completed_documents = doc_completed.scalar()

        doc_failed = await db.execute(
            select(func.count()).select_from(Document).where(Document.status == DocumentStatus.FAILED)
        )
        failed_documents = doc_failed.scalar()

        # 用户统计
        user_total = await db.execute(select(func.count()).select_from(User))
        total_users = user_total.scalar()

        user_admin = await db.execute(
            select(func.count()).select_from(User).where(User.is_admin == True)
        )
        admin_users = user_admin.scalar()

        return {
            "documents": {
                "total": total_documents,
                "pending": pending_documents,
                "processing": processing_documents,
                "completed": completed_documents,
                "failed": failed_documents
            },
            "users": {
                "total": total_users,
                "admins": admin_users,
                "regular": total_users - admin_users
            },
            "timestamp": time.time()
        }

    except Exception as e:
        logger.exception(f"获取系统统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


@router.get("/mineru/stats", response_model=MinerUNodeStats, summary="获取MinerU节点统计信息")
async def get_mineru_stats(
        current_user: dict = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """获取MinerU节点的统计信息，包括节点数量、任务数量等"""
    if not current_user.get("is_admin"):
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        # 获取所有节点
        result = await db.execute(select(MinerUNode))
        nodes = result.scalars().all()

        # 只统计启用的节点
        enabled_nodes = [node for node in nodes if node.is_enabled]

        # 计算统计数据
        total_nodes = len(enabled_nodes)  # 只统计启用的节点
        online_nodes = sum(1 for node in enabled_nodes if node.status == NodeStatus.ONLINE)
        offline_nodes = sum(1 for node in enabled_nodes if node.status == NodeStatus.OFFLINE)
        busy_nodes = sum(1 for node in enabled_nodes if node.status == NodeStatus.BUSY)
        error_nodes = sum(1 for node in enabled_nodes if node.status == NodeStatus.ERROR)

        # 计算任务统计（包括所有节点的历史数据）
        total_tasks = sum(node.total_tasks for node in nodes)
        success_tasks = sum(node.success_tasks for node in nodes)
        failed_tasks = sum(node.failed_tasks for node in nodes)
        running_tasks = sum(node.current_tasks for node in enabled_nodes)  # 只统计启用节点的当前任务

        # 计算成功率
        success_rate = round((success_tasks / total_tasks * 100) if total_tasks > 0 else 0, 2)

        logger.info(f"节点统计 - 总数: {total_nodes}, 在线: {online_nodes}, 离线: {offline_nodes}, "
                    f"繁忙: {busy_nodes}, 错误: {error_nodes}")
        logger.info(f"任务统计 - 总数: {total_tasks}, 成功: {success_tasks}, "
                    f"失败: {failed_tasks}, 运行中: {running_tasks}")

        return {
            "total_nodes": total_nodes,
            "online_nodes": online_nodes,
            "offline_nodes": offline_nodes,
            "busy_nodes": busy_nodes,
            "error_nodes": error_nodes,
            "total_tasks": total_tasks,
            "success_tasks": success_tasks,
            "failed_tasks": failed_tasks,
            "running_tasks": running_tasks,
            "success_rate": success_rate
        }
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
