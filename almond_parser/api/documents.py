# -*- encoding: utf-8 -*-
"""
文档管理 API
"""
import os
import uuid
from pathlib import Path
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from fastapi import status as http_status
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from almond_parser.config import settings
from almond_parser.db import get_db, ApiKey
from almond_parser.schemas.document import (
    DocumentResponse, DocumentQueryParams, DocumentListResponse,
    DocumentRetryRequest, BatchTaskResponse, BatchStatusResponse
)
from almond_parser.schemas.base import BaseResponse
from almond_parser.services.document_service import DocumentService
from almond_parser.utils.auth import get_current_user, get_current_api_key
from almond_parser.tasks.arq_app import arq_manager

router = APIRouter(prefix="/document", tags=["文档管理"])

# 支持的文件类型
SUPPORTED_EXTENSIONS = {".pdf", ".doc", ".docx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png"}


def is_supported_file(filename: str) -> bool:
    """检查文件类型是否支持"""
    return Path(filename).suffix.lower() in SUPPORTED_EXTENSIONS


@router.post("/upload", response_model=BatchTaskResponse)
async def upload_documents(
    files: List[UploadFile] = File(..., description="要上传的文件列表"),
    priority: int = Form(1, ge=1, le=10, description="任务优先级"),
    service_type: str = Form("auto", description="服务类型: auto/document/knowledge_base"),
    parse_mode: str = Form("auto", description="解析模式: auto/pipeline/sglang"),
    max_retries: int = Form(2, ge=0, le=5, description="最大重试次数"),
    parse_config: Optional[str] = Form(None, description="解析配置(JSON字符串)"),
    db: AsyncSession = Depends(get_db),
    current_user: ApiKey = Depends(get_current_api_key)  # 使用 API Key 认证
):
    """
    批量上传文档并提交解析任务

    - **files**: 要上传的文件列表（支持 .pdf, .doc, .docx, .ppt, .pptx, .jpg, .jpeg, .png）
    - **priority**: 任务优先级 (1-10，数字越大优先级越高)
    - **service_type**: 服务类型 (auto: 自动选择, document: 文档解析, knowledge_base: 知识库解析)
    - **parse_mode**: 解析模式 (auto: 自动选择, pipeline: Pipeline模式, sglang: SGLang模式)
    - **max_retries**: 最大重试次数 (0-5)
    - **parse_config**: 解析配置参数（可选）
    """
    try:
        # 确保 ARQ 已初始化
        await arq_manager.initialize()

        if not files:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="请至少上传一个文件"
            )

        # 检查文件数量限制
        if len(files) > 50:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="单次最多上传50个文件"
            )

        # 生成批次ID
        batch_id = str(uuid.uuid4())
        user_id = str(current_user.user_id)

        logger.info(f"开始批量上传，用户: {user_id}, 批次: {batch_id}, 文件数: {len(files)}")

        # 确保上传目录存在
        upload_dir = Path(settings.UPLOAD_DIR)
        upload_dir.mkdir(parents=True, exist_ok=True)

        service = DocumentService(db)
        uploaded_files = []
        failed_files = []

        # 处理每个文件
        for file in files:
            try:
                # 检查文件类型
                if not is_supported_file(file.filename):
                    failed_files.append({
                        "filename": file.filename,
                        "error": f"不支持的文件类型: {Path(file.filename).suffix}"
                    })
                    continue

                # 检查文件大小
                content = await file.read()
                if len(content) > settings.MAX_FILE_SIZE:
                    failed_files.append({
                        "filename": file.filename,
                        "error": f"文件大小超过限制: {len(content)} > {settings.MAX_FILE_SIZE}"
                    })
                    continue

                # 生成唯一文件名
                file_ext = Path(file.filename).suffix
                saved_filename = f"{uuid.uuid4()}{file_ext}"
                file_path = upload_dir / saved_filename

                # 保存文件
                with open(file_path, "wb") as f:
                    f.write(content)

                # 创建文档记录
                document = await service.create_document(
                    batch_id=batch_id,
                    user_id=user_id,
                    filename=file.filename,
                    file_size=len(content),
                    file_type=file_ext[1:].lower(),
                    file_path=str(file_path),
                    parse_config=parse_config
                )

                uploaded_files.append({
                    "document_id": document.document_id,
                    "filename": file.filename,
                    "status": "uploaded"
                })

                logger.info(f"文件上传成功: {file.filename} -> {document.document_id}")

            except Exception as e:
                logger.error(f"处理文件失败: {file.filename}, 错误: {e}")
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e)
                })

        # 如果有成功上传的文件，提交批量解析任务
        if uploaded_files:
            job_id = await arq_manager.enqueue_task(
                "process_batch_documents",
                batch_id=batch_id,
                user_id=user_id,
                priority=priority,
                service_type=service_type,
                parse_mode=parse_mode,
                max_retries=max_retries
            )

            logger.info(f"批量解析任务已提交: {batch_id}, 任务ID: {job_id}, 服务类型: {service_type}")

        return BatchTaskResponse(
            success=True,
            message=f"批量上传完成，成功: {len(uploaded_files)}, 失败: {len(failed_files)}",
            batch_id=batch_id,
            task_count=len(uploaded_files),
            uploaded_files=uploaded_files,
            failed_files=failed_files if failed_files else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量上传失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量上传失败: {str(e)}"
        )


@router.get("/documents", response_model=DocumentListResponse)
async def get_documents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    document_id: Optional[str] = Query(None, description="文档ID"),
    batch_id: Optional[str] = Query(None, description="批次ID"),
    status: Optional[str] = Query(None, description="文档状态"),
    file_name: Optional[str] = Query(None, description="文件名"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    获取文档列表

    支持分页、筛选和排序功能
    """
    try:
        # 处理空字符串参数
        processed_status = status if status and status.strip() else None
        processed_document_id = document_id if document_id and document_id.strip() else None
        processed_batch_id = batch_id if batch_id and batch_id.strip() else None
        processed_file_name = file_name if file_name and file_name.strip() else None

        # 构建查询参数
        query_params = DocumentQueryParams(
            page=page,
            page_size=page_size,
            document_id=processed_document_id,
            batch_id=processed_batch_id,
            status=processed_status,
            file_name=processed_file_name,
            user_id=str(current_user["user_id"]),
            sort_by=sort_by,
            sort_order=sort_order
        )

        service = DocumentService(db)
        result = await service.get_documents(query_params)

        logger.info(f"用户 {current_user['username']} 查询文档列表: {result.pagination.total} 条记录")
        return result

    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文档列表失败"
        )


@router.get("/documents/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取单个文档详情"""
    try:
        service = DocumentService(db)
        document = await service.get_document(document_id, str(current_user["user_id"]))

        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        return document

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档详情失败: {document_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文档详情失败"
        )


@router.post("/documents/{document_id}/retry", response_model=BaseResponse)
async def retry_document(
    document_id: str,
    retry_request: DocumentRetryRequest,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """重试文档处理"""
    try:
        service = DocumentService(db)

        # 检查文档是否存在且属于当前用户
        document = await service.get_document(document_id, str(current_user["user_id"]))
        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 提交重试任务
        job_id = await arq_manager.enqueue_task(
            "retry_document",
            document_id=document_id,
            user_id=str(current_user["user_id"]),
            force=retry_request.force
        )

        logger.info(f"用户 {current_user['username']} 重试文档: {document_id}, 任务ID: {job_id}")
        return BaseResponse(message=f"文档重试任务已提交，任务ID: {job_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试文档失败: {document_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f"重试文档失败: {str(e)}"
        )


@router.get("/batch/{batch_id}", response_model=BatchStatusResponse)
async def get_batch_status(
    batch_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取批次任务状态"""
    try:
        service = DocumentService(db)
        batch_status = await service.get_batch_status(batch_id, str(current_user["user_id"]))

        if not batch_status:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="批次不存在"
            )

        return batch_status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取批次状态失败: {batch_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取批次状态失败"
        )


@router.delete("/documents/{document_id}", response_model=BaseResponse)
async def delete_document(
    document_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """删除文档"""
    try:
        service = DocumentService(db)
        success = await service.delete_document(document_id, str(current_user["user_id"]))

        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        logger.info(f"用户 {current_user['username']} 删除文档: {document_id}")
        return BaseResponse(message="文档删除成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文档失败: {document_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f"删除文档失败: {str(e)}"
        )


@router.get("/documents/{document_id}/logs")
async def get_document_logs(
    document_id: str,
    limit: int = Query(100, ge=1, le=1000, description="日志条数限制"),
    offset: int = Query(0, ge=0, description="日志偏移量"),
    level: Optional[str] = Query(None, description="日志级别过滤"),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取文档处理日志"""
    try:
        service = DocumentService(db)

        # 检查文档权限
        document = await service.get_document(document_id, str(current_user["user_id"]))
        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        logs = await service.get_document_logs(document_id, limit, offset, level)
        return {
            "document_id": document_id,
            "logs": logs,
            "has_more": len(logs) == limit
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档日志失败: {document_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文档日志失败"
        )


@router.post("/callback", response_model=BaseResponse)
async def receive_parse_result(
    result_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_api_key)  # mineru-api 回调使用 API Key
):
    """
    接收 mineru-api 的解析结果回调

    - **document_id**: 文档ID
    - **result_data**: 解析结果数据
    """
    task_id = None
    try:
        logger.info(f"接受到回调: {result_data}")
        service = DocumentService(db)
        if "task_id" not in result_data:
            logger.warning(f"回调数据中未包含 task_id: {result_data}")
            return BaseResponse(message="解析结果已更新")
        task_id = result_data["task_id"]
        # 更新文档解析结果
        success = await service.update_document_result(task_id, result_data)

        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        logger.info(f"接收到文档解析结果: {task_id}")
        return BaseResponse(message="解析结果已更新")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理解析结果回调失败: {task_id}, 错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="处理解析结果失败"
        )


@router.get("/{document_id}/result")
async def get_parse_result(
    document_id: str,
    content_type: str = Query("json", enum=["json", "file"], description="返回类型"),
    db: AsyncSession = Depends(get_db),
    # current_user: dict = Depends(get_current_user)  # 使用用户认证，支持前端调用
):
    """
    获取文档解析结果
http://localhost:5173/api/v1/document/6da2e8d01af64f758184c66cd47bdd07/result?content_type=json
    - **document_id**: 文档ID
    - **content_type**: 返回类型 (json: JSON格式, file: 文件下载)
    """
    try:
        service = DocumentService(db)

        # 检查文档权限
        document = await service.get_document(document_id,)
        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 检查解析状态
        if document.status != "COMPLETED":
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"文档尚未解析完成，当前状态: {document.status}"
            )

        if not document.result_data:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="解析结果不存在"
            )


        if content_type == "json":
            md_file = Path(document.output_path)
            if md_file.exists():
                markdown_text = md_file.read_text(encoding="utf-8")
            else:
                logger.warning(f"Markdown 文件不存在: {md_file}")
                raise HTTPException(
                    status_code=http_status.HTTP_404_NOT_FOUND,
                    detail="解析结果文件不存在"
                )
            return {
                "document_id": document_id,
                "status": document.status,
                "result": {
                    "markdown_text": markdown_text
                },
                "completed_at": document.completed_at
            }
        else:
            # 返回文件下载
            from fastapi.responses import FileResponse

            result_file = document.output_path
            if not result_file or not os.path.exists(result_file):
                raise HTTPException(
                    status_code=http_status.HTTP_404_NOT_FOUND,
                    detail="解析结果文件不存在"
                )

            return FileResponse(
                path=result_file,
                media_type="application/octet-stream",
                filename=f"{document.file_name}_result.md"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取解析结果失败: {document_id}, 错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取解析结果失败"
        )


@router.post("/documents/{document_id}/query-status", response_model=BaseResponse)
async def query_parse_status(
    document_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    手动查询文档解析状态（主动向 mineru-api 查询）

    - **document_id**: 文档ID
    """
    try:
        service = DocumentService(db)

        # 检查文档权限
        document = await service.get_document(document_id, str(current_user["user_id"]))
        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 提交状态查询任务
        job_id = await arq_manager.enqueue_task(
            "query_document_status",
            document_id=document_id,
            user_id=str(current_user["user_id"])
        )

        logger.info(f"用户 {current_user['username']} 查询文档状态: {document_id}, 任务ID: {job_id}")
        return BaseResponse(message=f"状态查询任务已提交，任务ID: {job_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询解析状态失败: {document_id}, 错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f"查询解析状态失败: {str(e)}"
        )
