# -*- encoding: utf-8 -*-
"""
文档相关 Pydantic 模型
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field

from .base import BaseResponse, PaginationParams, PaginationResponse, TimestampMixin, IDMixin


class DocumentStatus(str, Enum):
    """文档状态枚举"""
    UPLOADING = "UPLOADING"  # 上传中
    UPLOADED = "UPLOADED"  # 已上传
    PARSING = "PARSING"  # 解析中
    COMPLETED = "COMPLETED"  # 已完成
    FAILED = "FAILED"  # 失败
    RETRY_PENDING = "RETRY_PENDING"  # 等待重试
    FALLBACK_RETRY = "FALLBACK_RETRY"  # 降级重试中


class DocumentBase(BaseModel):
    """文档基础模型"""
    document_id: str = Field(description="文档ID")
    batch_id: str = Field(description="批次ID")
    file_name: str = Field(description="文件名")
    file_size: Optional[int] = Field(default=None, description="文件大小")
    file_type: Optional[str] = Field(default=None, description="文件类型")
    status: DocumentStatus = Field(default=DocumentStatus.UPLOADED, description="文档状态")


class DocumentCreate(DocumentBase):
    """创建文档模型"""
    user_id: str = Field(description="用户ID")
    node_id: Optional[int] = Field(default=None, description="处理节点ID")


class DocumentUpdate(BaseModel):
    """更新文档模型"""
    status: Optional[DocumentStatus] = Field(default=None, description="文档状态")
    progress: Optional[int] = Field(default=None, ge=0, le=100, description="处理进度")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    result_data: Optional[Dict[str, Any]] = Field(default=None, description="解析结果")


class DocumentResponse(DocumentBase, IDMixin, TimestampMixin):
    """文档响应模型"""
    user_id: str = Field(description="用户ID")
    node_id: Optional[int] = Field(default=None, description="处理节点ID")
    progress: Optional[int] = Field(default=None, description="处理进度")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    result_data: Optional[Dict[str, Any]] = Field(default=None, description="解析结果")
    output_path: Optional[str] = Field(default=None, description="输出文件路径")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")

    class Config:
        from_attributes = True


class DocumentQueryParams(PaginationParams):
    """文档查询参数"""
    document_id: Optional[str] = Field(default=None, description="文档ID")
    batch_id: Optional[str] = Field(default=None, description="批次ID")
    status: Optional[DocumentStatus] = Field(default=None, description="文档状态")
    file_name: Optional[str] = Field(default=None, description="文件名")
    user_id: Optional[str] = Field(default=None, description="用户ID")
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")


class DocumentListResponse(BaseResponse):
    """文档列表响应"""
    items: List[DocumentResponse] = Field(description="文档列表")
    pagination: PaginationResponse = Field(description="分页信息")


class DocumentRetryRequest(BaseModel):
    """文档重试请求"""
    force: bool = Field(default=False, description="是否强制重试")


class DocumentLogLine(BaseModel):
    """文档日志行"""
    timestamp: datetime = Field(description="时间戳")
    level: str = Field(description="日志级别")
    message: str = Field(description="日志消息")
    source: Optional[str] = Field(default=None, description="日志来源")


class DocumentLogResponse(BaseModel):
    """文档日志响应"""
    document_id: str = Field(description="文档ID")
    logs: List[DocumentLogLine] = Field(description="日志列表")
    has_more: bool = Field(default=False, description="是否有更多日志")


class BatchTaskRequest(BaseModel):
    """批量任务请求"""
    files: List[str] = Field(description="文件列表")
    batch_id: Optional[str] = Field(default=None, description="批次ID")
    priority: int = Field(default=1, ge=1, le=10, description="任务优先级")


class BatchTaskResponse(BaseResponse):
    """批量任务响应"""
    batch_id: str = Field(description="批次ID")
    task_count: int = Field(description="任务数量")
    estimated_time: Optional[int] = Field(default=None, description="预估完成时间(秒)")
    uploaded_files: Optional[List[dict]] = Field(default=None, description="成功上传的文件")
    failed_files: Optional[List[dict]] = Field(default=None, description="失败的文件")


class BatchStatusResponse(BaseResponse):
    """批次状态响应"""
    batch_id: str = Field(description="批次ID")
    total: int = Field(description="总任务数")
    completed: int = Field(description="已完成数")
    failed: int = Field(description="失败数")
    progress: float = Field(description="完成进度")
    documents: List[DocumentResponse] = Field(description="文档列表")
