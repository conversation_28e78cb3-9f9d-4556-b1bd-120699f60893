# -*- encoding: utf-8 -*-
"""
API Key 服务
"""
from datetime import datetime, timedelta
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from loguru import logger

from almond_parser.db.models import Api<PERSON>ey
from almond_parser.schemas.api_key import ApiKeyCreate, ApiKeyUpdate, ApiKeyResponse
from almond_parser.utils.auth import generate_api_key


class ApiKeyService:
    """API Key 服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_api_key(self, user_id: int, api_key_data: ApiKeyCreate) -> ApiKeyResponse:
        """创建 API Key"""
        try:
            # 生成 API Key
            key = generate_api_key()
            
            # 计算过期时间
            expires_at = None
            if api_key_data.expires_days:
                expires_at = datetime.now() + timedelta(days=api_key_data.expires_days)
            
            # 创建数据库记录
            api_key = ApiKey(
                name=api_key_data.name,
                key=key,
                user_id=user_id,
                expires_at=expires_at,
                is_enabled=True,
                usage_count=0
            )
            
            self.db.add(api_key)
            await self.db.commit()
            await self.db.refresh(api_key)
            
            logger.info(f"创建 API Key 成功: {api_key.name} (用户: {user_id})")
            return ApiKeyResponse.model_validate(api_key)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建 API Key 失败: {e}")
            raise
    
    async def get_user_api_keys(self, user_id: int) -> List[ApiKeyResponse]:
        """获取用户的所有 API Key"""
        try:
            result = await self.db.execute(
                select(ApiKey)
                .where(ApiKey.user_id == user_id)
                .order_by(ApiKey.created_at.desc())
            )
            api_keys = result.scalars().all()
            
            return [ApiKeyResponse.model_validate(api_key) for api_key in api_keys]
            
        except Exception as e:
            logger.error(f"获取用户 API Key 失败: {e}")
            raise
    
    async def get_api_key(self, api_key_id: int, user_id: int) -> Optional[ApiKeyResponse]:
        """获取单个 API Key"""
        try:
            result = await self.db.execute(
                select(ApiKey)
                .where(and_(ApiKey.id == api_key_id, ApiKey.user_id == user_id))
            )
            api_key = result.scalar_one_or_none()
            
            if api_key:
                return ApiKeyResponse.model_validate(api_key)
            return None
            
        except Exception as e:
            logger.error(f"获取 API Key 失败: {e}")
            raise
    
    async def update_api_key(
        self, 
        api_key_id: int, 
        user_id: int, 
        api_key_data: ApiKeyUpdate
    ) -> Optional[ApiKeyResponse]:
        """更新 API Key"""
        try:
            result = await self.db.execute(
                select(ApiKey)
                .where(and_(ApiKey.id == api_key_id, ApiKey.user_id == user_id))
            )
            api_key = result.scalar_one_or_none()
            
            if not api_key:
                return None
            
            # 更新字段
            if api_key_data.name is not None:
                api_key.name = api_key_data.name
            if api_key_data.is_enabled is not None:
                api_key.is_enabled = api_key_data.is_enabled
            
            api_key.updated_at = datetime.now()
            
            await self.db.commit()
            await self.db.refresh(api_key)
            
            logger.info(f"更新 API Key 成功: {api_key.name} (用户: {user_id})")
            return ApiKeyResponse.model_validate(api_key)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新 API Key 失败: {e}")
            raise
    
    async def delete_api_key(self, api_key_id: int, user_id: int) -> bool:
        """删除 API Key"""
        try:
            result = await self.db.execute(
                select(ApiKey)
                .where(and_(ApiKey.id == api_key_id, ApiKey.user_id == user_id))
            )
            api_key = result.scalar_one_or_none()
            
            if not api_key:
                return False
            
            await self.db.delete(api_key)
            await self.db.commit()
            
            logger.info(f"删除 API Key 成功: {api_key.name} (用户: {user_id})")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除 API Key 失败: {e}")
            raise
    
    async def verify_api_key(self, key: str) -> Optional[ApiKey]:
        """验证 API Key"""
        try:
            result = await self.db.execute(
                select(ApiKey)
                .where(and_(
                    ApiKey.key == key,
                    ApiKey.is_enabled == True
                ))
            )
            api_key = result.scalar_one_or_none()
            
            if not api_key:
                return None
            
            # 检查是否过期
            if api_key.expires_at and api_key.expires_at < datetime.now():
                return None
            
            # 更新使用统计
            api_key.usage_count += 1
            api_key.last_used_at = datetime.now()
            await self.db.commit()
            
            return api_key
            
        except Exception as e:
            logger.error(f"验证 API Key 失败: {e}")
            raise
    
    async def get_api_key_stats(self, user_id: int) -> dict:
        """获取 API Key 统计信息"""
        try:
            result = await self.db.execute(
                select(ApiKey)
                .where(ApiKey.user_id == user_id)
            )
            api_keys = result.scalars().all()
            
            total_count = len(api_keys)
            enabled_count = sum(1 for key in api_keys if key.is_enabled)
            expired_count = sum(
                1 for key in api_keys 
                if key.expires_at and key.expires_at < datetime.now()
            )
            total_usage = sum(key.usage_count for key in api_keys)
            
            return {
                "total_count": total_count,
                "enabled_count": enabled_count,
                "disabled_count": total_count - enabled_count,
                "expired_count": expired_count,
                "total_usage": total_usage
            }
            
        except Exception as e:
            logger.error(f"获取 API Key 统计失败: {e}")
            raise
    
    async def get_or_create_default_key(self, user_id: int) -> ApiKeyResponse:
        """获取或创建默认的 API Key"""
        try:
            # 先查找是否存在默认的 API Key
            result = await self.db.execute(
                select(ApiKey)
                .where(and_(
                    ApiKey.user_id == user_id,
                    ApiKey.is_enabled == True,
                    ApiKey.name == "默认 API Key"
                ))
                .order_by(ApiKey.created_at.desc())
            )
            api_key = result.scalar_one_or_none()
            
            if api_key:
                return ApiKeyResponse.model_validate(api_key)
            
            # 如果不存在，创建一个新的永久 API Key
            api_key_data = ApiKeyCreate(
                name="默认 API Key",
                expires_days=None  # 永不过期
            )
            return await self.create_api_key(user_id, api_key_data)
            
        except Exception as e:
            logger.error(f"获取或创建默认 API Key 失败: {e}")
            raise
